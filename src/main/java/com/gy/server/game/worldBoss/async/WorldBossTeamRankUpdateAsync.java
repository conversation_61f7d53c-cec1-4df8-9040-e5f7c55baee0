package com.gy.server.game.worldBoss.async;

import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.worldBoss.WorldBossHelper;
import com.gy.server.game.worldBoss.stage.WorldBossStage;
import com.gy.server.game.worldBoss.bean.WorldBossTeamRankInfo;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.TLBase;

import java.util.Map;

/**
 * 武林悬赏 房间内排行榜伤害更新
 *
 * <AUTHOR> 2024/4/25 11:09
 **/
public class WorldBossTeamRankUpdateAsync extends AsyncCall {

    private long playerId;
    private WorldBossStage stage;

    private Map<HeroUnit, Long> heroUnitLongMap;

    public WorldBossTeamRankUpdateAsync(long playerId, WorldBossStage stage) {
        this.playerId = playerId;
        this.stage = stage;
        this.heroUnitLongMap = CombatManager.getStageRecordHeroesHurtByStage(stage, true);
    }

    @Override
    public void execute() {

    }

    @Override
    public void asyncExecute() {
        if(CollectionUtil.isNotEmpty(heroUnitLongMap)){
            //更新伤害数据
            long roomId = stage.getRoomId();
            WorldBossTeamRankInfo worldBossTeamRankInfo = new WorldBossTeamRankInfo(playerId, heroUnitLongMap);

            String key = WorldBossHelper.getRoomRankRedisKey(roomId);
            TLBase.getInstance().getRedisAssistant().hashPut(key, playerId + "", worldBossTeamRankInfo);
            TLBase.getInstance().getRedisAssistant().pexpire(key, DateTimeUtil.MillisOfMinute * 4);
        }
    }
}
