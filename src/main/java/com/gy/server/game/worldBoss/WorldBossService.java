package com.gy.server.game.worldBoss;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.common.ConstantConfigReader;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.function.Function;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.reddot.RedDot;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.game.worldBoss.async.WorldBossHurtRankInfoAsync;
import com.gy.server.game.worldBoss.async.WorldBossTeamRankNotifyAsync;
import com.gy.server.game.worldBoss.template.WorldBossConstantTemplate;
import com.gy.server.game.worldBoss.template.WorldBossMainTemplate;
import com.gy.server.game.worldBoss.template.WorldBossRankPointTemplate;
import com.gy.server.game.worldBoss.template.WorldBossRankTemplate;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.time.DayOfWeek;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 武林悬赏令
 * <AUTHOR> - [Created on 2023-08-14 16:56]
 */
public class WorldBossService extends PlayerPacketHandler implements Service {

    /**
     * key:地点id  value:WorldBossMainTemplate
     */
    private static Map<Integer, WorldBossMainTemplate> mainMap = new HashMap<>();

    /**
     * 周几对应地点id
     * key 周几，value 地点id
     */
    private static Map<Integer, Integer> weekOpenPlaceIdsMap = new HashMap<>();

    /**
     * 段位
     * key:段位   value:段位模版
     */
    private static Map<Integer, WorldBossRankTemplate> rankMap = new HashMap<>();

    private static Map<Integer, WorldBossRankPointTemplate> rankPointMap = new HashMap<>();

    /**
     * 常量配置
     */
    private static WorldBossConstantTemplate constant;

    /**
     * 伤害排行榜条数    存1000个显示100
     */
    public static final int requireNum = 1000;
    /**
     * 伤害排行榜显示条数
     */
    public static final int showNum = 100;

    private static int getNowWeek(){
        DayOfWeek dayOfWeek = ServerConstants.getCurrentTimeLocalDateTime().getDayOfWeek();
        return dayOfWeek.getValue();
    }

    /**
     * 信息
     * @param player
     * @param req
     */
    @Handler(PtCode.WORLD_BOSS_GET_INFO_REQ)
    private void getBossInfo(Player player, PbProtocol.WorldBossGetInfoReq req, long time){
        PbProtocol.WorldBossGetInfoRst.Builder rst = PbProtocol.WorldBossGetInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            //检查function是否解锁
            if(!Function.worldBoss.isOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.武林悬赏令未解锁));
                break logic;
            }

            int hour = ServerConstants.getCurrentTimeLocalDateTime().getHour();
            if(hour < getStartClock() || hour > getEndClock()){
                rst.setResult(Text.genServerRstInfo(Text.武林悬赏令未到开启时间));
                break logic;
            }

            PlayerWorldBossModel model = player.getWorldBossModel();
            rst.setWorldBoss(model.genPb());
        }

        player.send(PtCode.WORLD_BOSS_GET_INFO_RST, rst.build(), time);
    }


    /**
     * 获取排行榜信息 房间排行榜
     * @param player
     * @param req
     */
    @Handler(PtCode.WORLD_BOSS_RANK_REQ)
    private void worldBossRankReq(Player player, PbProtocol.WorldBossRankReq req, long time){
        ThreadPool.execute(new WorldBossTeamRankNotifyAsync(player.getPlayerId(),  req.getRoomId(), false, time));
    }

    /**
     * 领取段位奖励
     * @param player
     * @param req
     * @param time
     */
    @Handler(PtCode.WORLD_BOSS_DRAW_DAN_REWARD_REQ)
    private void worldBossDrawDanRewardReq(Player player, PbProtocol.WorldBossDrawDanRewardReq req, long time) {
        PbProtocol.WorldBossDrawDanRewardRst.Builder rst = PbProtocol.WorldBossDrawDanRewardRst.newBuilder().setResult(Text.genOkServerRstInfo());
        int rankId = req.getRankId();

        logic:{
            //检查function是否解锁
            if(!Function.worldBoss.isOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.武林悬赏令未解锁));
                break logic;
            }

            int hour = ServerConstants.getCurrentTimeLocalDateTime().getHour();
            if(hour < getStartClock() || hour > getEndClock()){
                rst.setResult(Text.genServerRstInfo(Text.武林悬赏令未到开启时间));
                break logic;
            }

            WorldBossRankTemplate rankTemplate = rankMap.get(rankId);
            if(Objects.isNull(rankTemplate)){
                rst.setResult(Text.genServerRstInfo(Text.对应的模板数据找不到));
                break logic;
            }

            PlayerWorldBossModel model = player.getWorldBossModel();
            if(model.getDanIds().contains(rankId)){
                rst.setResult(Text.genServerRstInfo(Text.奖励已领取));
                break logic;
            }

            if(model.getMaxDan() < rankId){
                rst.setResult(Text.genServerRstInfo(Text.条件未满足));
                break logic;
            }

            //需要打一场
            WorldBossGlobalData globalData = GlobalDataManager.getData(GlobalDataType.worldBoss);
            if(!globalData.getRankInfoMap().containsKey(player.getPlayerId())){
                rst.setResult(Text.genServerRstInfo(Text.条件未满足));
                break logic;
            }

            List<RewardTemplate> rewardTemplates = rankTemplate.rankReward;
            if(rewardTemplates.isEmpty()){
                rst.setResult(Text.genServerRstInfo(Text.武林悬赏令当前段位无奖励));
                break logic;
            }
            List<Reward> rewards = Reward.templateCollectionToReward(rewardTemplates);

            model.getDanIds().add(rankId);
            Reward.add(rewards, player, BehaviorType.worldBossDanReward);
            List<PbCommons.PbReward> pbRewards = Reward.writeCollectionToPb(rewards);
            rst.addAllReward(pbRewards);

            player.dataSyncModule.syncWorldBossSyncData();
            RedDot.wulinRankReward.sync(player);
        }

        player.send(PtCode.WORLD_BOSS_DRAW_DAN_REWARD_RST, rst.build(), time);
    }

    /**
     * 武林悬赏伤害排行榜信息
     */
    @Handler(PtCode.WORLD_BOSS_HURT_RANK_INFO_REQ)
    private void worldBossHurtRankInfoReq(Player player, PbProtocol.WorldBossHurtRankInfoReq req, long time){
        ThreadPool.execute(new WorldBossHurtRankInfoAsync(player, req, time));
    }

    /**
     * 根据排名获取积分奖励
     * @param rank
     * @return
     */
    public static int getPointReward(int rank){
        for (WorldBossRankPointTemplate bean : rankPointMap.values()) {
            if(rank >= bean.rank.getLeft() && rank <= bean.rank.getRight()){
                return bean.points;
            }
        }
        return 0;
    }

    /**
     * 根据排行段位和房间排行获取奖励模版
     * @param rankId
     * @param teamRank
     * @return
     */
    public static List<RewardTemplate> getRankRewardTemplateByRankIdAndTeamRank(int rankId, int teamRank){
        WorldBossRankTemplate template = rankMap.get(rankId);
        return template.rewardMap.containsKey(teamRank) ? template.rewardMap.get(teamRank) : template.rewardMap.lastEntry().getValue(); //TODO这
    }

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        Map<String, String> constantMap = ConstantConfigReader.read(ConfigFile.worldBoss_constant);
        WorldBossConstantTemplate constantTemp = new WorldBossConstantTemplate(constantMap);
        constant = constantTemp;

        Map<Integer, WorldBossMainTemplate> mainTemplateMapTemp = new HashMap();
        Map<Integer, Integer> weekOpenPlaceIdsMapTemp = new HashMap();
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.worldBoss_worldBossMain);
        for (Map<String, String> map : mapList){
            WorldBossMainTemplate template = new WorldBossMainTemplate(map);
            mainTemplateMapTemp.put(template.placeId, template);

            weekOpenPlaceIdsMapTemp.put(template.open, template.placeId);
        }
        mainMap = mainTemplateMapTemp;
        weekOpenPlaceIdsMap = weekOpenPlaceIdsMapTemp;

        mapList = ConfigReader.read(ConfigFile.worldBoss_worldBossRank);
        Map<Integer, WorldBossRankTemplate> rankTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            WorldBossRankTemplate rewardTemplate = new WorldBossRankTemplate(map);
            rankTemplateMapTemp.put(rewardTemplate.rankId, rewardTemplate);
        }
        rankMap = rankTemplateMapTemp;

        mapList = ConfigReader.read(ConfigFile.worldBoss_rankPoint);
        Map<Integer, WorldBossRankPointTemplate> rankPointMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            WorldBossRankPointTemplate template = new WorldBossRankPointTemplate(map);
            rankPointMapTemp.put(template.id, template);
        }

    }

    @Override
    public void clearConfigData() {
        mainMap.clear();
        weekOpenPlaceIdsMap.clear();
        rankMap.clear();
        rankPointMap.clear();
    }

    /**
     * 获取当天开放的关卡id组
     * @return
     */
    public static int getOpenPlaceId(){
        int week = getNowWeek();
        return weekOpenPlaceIdsMap.getOrDefault(week, 0);
    }

    public static int getStartClock(){
        return constant.startClock;
    }

    public static int getEndClock(){
        return constant.endClock;
    }

    @Override
    public boolean isWorldServer() {
        return true;
    }

    public static Map<Integer, Integer> getWeekOpenPlaceIdsMap() {
        return weekOpenPlaceIdsMap;
    }

    public static Map<Integer, WorldBossMainTemplate> getMainMap() {
        return mainMap;
    }

    public static Map<Integer, WorldBossRankTemplate> getRankMap() {
        return rankMap;
    }

    public static Map<Integer, WorldBossRankPointTemplate> getRankPointMap() {
        return rankPointMap;
    }

    public static WorldBossConstantTemplate getConstant() {
        return constant;
    }
}
