package com.gy.server.game.qinPalace;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.activity.module.MazeActivityModule;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.common.ConstantConfigReader;
import com.gy.server.game.cond.CondManager;
import com.gy.server.game.currency.Currency;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.function.Function;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.liberty.LibertyHelper;
import com.gy.server.game.liberty.effect.LibertyType;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.qinPalace.bean.QinPalaceStoreyBean;
import com.gy.server.game.qinPalace.pointEvent.RgEventType;
import com.gy.server.game.qinPalace.specialEvent.RgSpecialEventsType;
import com.gy.server.game.qinPalace.specialEvent.SpecialEventManager;
import com.gy.server.game.qinPalace.template.*;
import com.gy.server.game.reddot.RedDot;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbQinPalace;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.MathUtil;
import com.ttlike.server.tl.baselib.serialize.combat.CombatSimpleUnitDb;
import com.ttlike.server.tl.baselib.serialize.qinPalace.QinEquipInfo;
import com.ttlike.server.tl.baselib.serialize.qinPalace.QinTalentInfo;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

/**
 * 秦皇地宫RG
 *
 * <AUTHOR> - [Created on 2022/11/1 9:35]
 */
@SuppressWarnings("DuplicatedCode")
public class QinPalaceRgService extends PlayerPacketHandler implements Service {

    private static Map<Integer, Map<Integer, RgTalentTemplate>> talentTemplateMap = new HashMap<>();
    private static Map<Integer, Map<Integer, RgRewardTemplate>> rewardTemplateMap = new HashMap<>();
    private static RgConstant rgConstant;
    private static Map<Integer, RgWayModelTemplate> wayModelTemplateMap = new HashMap<>();
    private static List<RgWayModelTemplate> randomWayModelTemplateList = new ArrayList<>();

    /**
     * key:层数   value:key:重数
     */
    private static Map<Integer, Map<Integer, Map<Integer, RgStoreyTemplate>>> storeyTemplateMap = new HashMap<>();
    private static Map<Integer, RgEquipDropTemplate> equipDropTemplateMap = new HashMap<>();
    private static Map<Integer, RgMonsterGroupTemplate> monsterGroupTemplateMap = new HashMap<>();
    // 根据怪物类型分组
    private static Map<RgEventType, List<RgMonsterGroupTemplate>> monsterGroupTemplateByTypeMap = new HashMap<>();
    // 对话事件
    private static Map<Integer, RgChatEventsTemplate> chatEventsTemplateMap = new HashMap<>();
    // 对话事件权重Map
    private static Map<RgChatEventsTemplate, Integer> chatEventsWeightMap = new HashMap<>();
    private static Map<Integer, RgSpecialEventsTemplate> specialEventsTemplateMap = new HashMap<>();
    // 无事发生事件ID
    private static int nothingSpecialEventId;
    private static Map<Integer, RgEquipTemplate> equipTemplateMap = new HashMap<>();
    private static Map<Integer, List<Integer>> equipQualityInfoMap = new HashMap<>();
    private static Map<Integer, RgDamnationTemplate> damnationTemplateMap = new HashMap<>();
    private static Map<RgDamnationTemplate, Integer> damnationTemplateWeightMap = new HashMap<>();
    private static Map<Integer, RgInfectTemplate> infectTemplateMap = new HashMap<>();
    private static Map<RgInfectTemplate, Integer> infectTemplateWeightMap = new HashMap<>();
    // 怪物属性成长系数 Table<层ID,重ID,Map<关卡ID, VO>>
    private static Table<Integer, Integer, Map<Integer, RgMonsterGrowthCoeTemplate>> worldLevelAttTemplateTable = HashBasedTable.create();
    private static Map<Integer, RgSeasonTemplate> seasonTemplateMap = new HashMap<>();
    private static Map<Integer, RgConditionTemplate> conditionTemplateMap = new HashMap<>();

    /**
     * 通用检查
     *
     * @return 错误码
     */
    private int beforeCheck(Player player) {
        if (!Function.qinPalace.isOpen(player)) {
            return Text.功能未开启;
        }
        QinPalaceGlobalData qinPalaceGlobalData = GlobalDataManager.getData(GlobalDataType.QinPalace);
        if (!qinPalaceGlobalData.isOpen()) {
            return Text.秦皇地宫赛季未开启;
        }
        return Text.没有异常;
    }


    /**
     * 秦皇地宫主界面信息
     */
    @Handler(PtCode.QIN_PALACE_MAIN_INFO_CLIENT)
    private void mainInfo(Player player, long time) {
        PbProtocol.QinPalaceMainInfoRst.Builder rst = PbProtocol.QinPalaceMainInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            if (!Function.qinPalace.isOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            PlayerQinPalaceModel qinPalaceModel = player.getModel(PlayerModelEnums.qinPalace);
            for (Map.Entry<Integer, QinTalentInfo> entry : qinPalaceModel.getTalentMap().entrySet()) {
                rst.putTalent(entry.getKey(), entry.getValue().getTalentLv());
            }
            QinPalaceGlobalData qinPalaceGlobalData = GlobalDataManager.getData(GlobalDataType.QinPalace);
            rst.setRoundId(qinPalaceGlobalData.getSeasonId());
            rst.setRoundStartTime(qinPalaceGlobalData.getTime());
            rst.setRoundEndTime(qinPalaceGlobalData.getEndTime());


            rst.putAllEquipGuides(qinPalaceModel.getEquipGuides());
            rst.addAllHasRewardId(qinPalaceModel.getHasRewardIdSet());
            rst.addAllHasFinishChatId(qinPalaceModel.getHasFinishChatEvent());
            rst.setCurLevelId(qinPalaceModel.getCurLevelId());
            rst.setCurStoreyId(qinPalaceModel.getCurStoryId());
            rst.setCurPointId(qinPalaceModel.getCurPointId());
            rst.addAllHasPassStoreyId(qinPalaceModel.getPassStoryIdSet());


            rst.setMapInfo(qinPalaceModel.buildMapPb());

            PbCommons.CombatSimpleUnitHp.Builder unitHp = PbCommons.CombatSimpleUnitHp.newBuilder();
            Map<CombatSimpleUnitDb, Double> heroRemainHpRateMap = qinPalaceModel.getHeroRemainHpRateMap();
            for (CombatSimpleUnitDb combatSimpleUnit : heroRemainHpRateMap.keySet()) {
                unitHp.clear();
                unitHp.setUnit(MazeActivityModule.convertPb(combatSimpleUnit));
                unitHp.setHp(heroRemainHpRateMap.get(combatSimpleUnit));
                rst.addUnitHps(unitHp.build());
            }
        }

        player.send(PtCode.QIN_PALACE_MAIN_INFO_SERVER, rst.build(), time);
    }

    /**
     * 秦皇地宫天赋升级
     */
    @Handler(PtCode.QIN_PALACE_TALENT_LV_CLIENT)
    private void talentLv(Player player, PbProtocol.QinPalaceTalentLvReq req, long time) {
        PbProtocol.QinPalaceTalentLvRst.Builder rst = PbProtocol.QinPalaceTalentLvRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            int result = beforeCheck(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            int talentId = req.getTalentId();
            Map<Integer, RgTalentTemplate> talentTempMap = getTalentTemplateMap();
            RgTalentTemplate rgTalentTemplate = talentTempMap.get(talentId);
            if (Objects.isNull(rgTalentTemplate)) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            Set<Integer> curRoundAllTalentSet = QinPalaceHelper.getCurRoundAllTalent();
            if (!curRoundAllTalentSet.contains(talentId)) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            PlayerQinPalaceModel playerQinPalaceModel = player.getModel(PlayerModelEnums.qinPalace);
            Map<Integer, QinTalentInfo> talentMap = playerQinPalaceModel.getTalentMap();
//            if (!talentMap.containsKey(talentId) && talentMap.size() >= rgConstant.wearTalentMax) {
//                rst.setResult(Text.genServerRstInfo(Text.佩戴天赋已达到上限));
//                break logic;
//            }
//            if (playerQinPalaceModel.getCurLevelId() != 0) {
//                rst.setResult(Text.genServerRstInfo(Text.当前存在正在进行的关卡不能升级天赋));
//                break logic;
//            }
            if (!talentMap.containsKey(talentId)) {
                // 判断是否解锁
                if (CondManager.checkNotCond(player, rgTalentTemplate.unlock)) {
                    rst.setResult(Text.genServerRstInfo(Text.秦皇地宫天赋未解锁));
                    break logic;
                }
                //检查前置
                if(CollectionUtil.isNotEmpty(rgTalentTemplate.PreTalents)){
                    for (Integer preTalentId : rgTalentTemplate.PreTalents) {
                        if(preTalentId > 0 && !talentMap.containsKey(preTalentId)){
                            rst.setResult(Text.genServerRstInfo(Text.秦皇地宫前置天赋未解锁));
                            break logic;
                        }
                    }
                }

            } else {
                rst.setResult(Text.genServerRstInfo(Text.秦皇地宫天赋已满级));
                break logic;
            }
            List<Reward> costRewardList = Reward.templateCollectionToReward(rgTalentTemplate.talentUpUseCurrencyNum);
            if (Reward.check(player, costRewardList) != -1) {
                rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                break logic;
            }
            QinTalentInfo talentInfo = talentMap.computeIfAbsent(talentId, vo -> new QinTalentInfo(talentId));
            talentInfo.setTalentLv(1);
            Reward.remove(costRewardList, player, BehaviorType.qinPalaceTalentLv);
            rst.setTalentId(talentId);
            rst.setTalentLv(talentInfo.getTalentLv());
            //日志
            GameLogger.qinPalaceTalentUp(player, talentId, false);

            player.postEvent(PlayerEventType.qinTalentUp);
        }

        player.send(PtCode.QIN_PALACE_TALENT_LV_SERVER, rst.build(), time);
    }

    /**
     * 秦皇地宫进入地图
     */
    @Handler(PtCode.QIN_PALACE_ENTER_MAP_CLIENT)
    private void enterMap(Player player, PbProtocol.QinPalaceEnterMapReq req, long time) {
        PbProtocol.QinPalaceEnterMapRst.Builder rst = PbProtocol.QinPalaceEnterMapRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            int result = beforeCheck(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            int levelId = req.getLevelId();
            Map<Integer, Map<Integer, RgStoreyTemplate>> allStoreyTemplateMap = getAllStoreyTemplateMap();
            if (!allStoreyTemplateMap.containsKey(levelId)) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            PlayerQinPalaceModel playerQinPalaceModel = player.getModel(PlayerModelEnums.qinPalace);
            // 第一层ID
            int firstLevelId = Collections.min(allStoreyTemplateMap.keySet());
            // 判断上一层是否通关或者是否是第一层
            int preLevelId = levelId - 1;
            if (firstLevelId != levelId && !playerQinPalaceModel.getPassLevelIdSet().contains(preLevelId)) {
                rst.setResult(Text.genServerRstInfo(Text.请先通关上一层地宫));
                break logic;
            }
            // 判断本层是否解锁
            Map<Integer, RgStoreyTemplate> storeyTemplateMap = getStoreyTemplateMap(levelId);
            // 第一重ID
            int firstStoreyId = Collections.min(storeyTemplateMap.keySet());
            RgStoreyTemplate rgStoreyTemplate = storeyTemplateMap.get(firstStoreyId);
            QinPalaceGlobalData qinPalaceGlobalData = GlobalDataManager.getData(GlobalDataType.QinPalace);
            if (qinPalaceGlobalData.getOpenDays() < rgStoreyTemplate.openDay) {
                rst.setResult(Text.genServerRstInfo(Text.本层地宫还未解锁));
                break logic;
            }
            int curLevelId = playerQinPalaceModel.getCurLevelId();
            // 上次进程还未结束
            if (curLevelId != 0) {
                // 进入之前的进程继续挑战
                if (curLevelId == levelId && playerQinPalaceModel.getQinPalaceStoreyBean() != null) {
                    playerQinPalaceModel.syncMap();
                    break logic;
                } else {
                    //结束之前的进程
                    playerQinPalaceModel.clearCurProgress();
                }
            }
            playerQinPalaceModel.enterMap(levelId);
            playerQinPalaceModel.syncMap();
        }

        player.send(PtCode.QIN_PALACE_ENTER_MAP_SERVER, rst.build(), time);
    }

    /**
     * 结束当前关卡进程
     */
    @Handler(PtCode.QIN_PALACE_END_LEVEL_CLIENT)
    private void endLevel(Player player, long time) {
        PbProtocol.QinPalaceEndLevelRst.Builder rst = PbProtocol.QinPalaceEndLevelRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            int result = beforeCheck(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            PlayerQinPalaceModel qinPalaceModel = player.getModel(PlayerModelEnums.qinPalace);
            if (qinPalaceModel.getCurLevelId() == 0) {
                rst.setResult(Text.genServerRstInfo(Text.当前没有正在进行的进程无需重置));
                break logic;
            }
            //计算奖励
            Pair<Integer, Integer> rewardInfo = qinPalaceModel.endLevelReward();
            int talentCoin = rewardInfo.getKey();
            int dungeonCoin = rewardInfo.getValue();
            if(talentCoin > 0 || dungeonCoin > 0){
                Reward reward1 = Reward.create(Currency.qinPalaceTalentCoin, talentCoin);
                Reward reward2 = Reward.create(Currency.dungeonCoin, dungeonCoin);
                reward1.checkMax(player);
                reward2.checkMax(player);
                reward1.add(player, BehaviorType.qinPalaceEndLevel);
                reward2.add(player, BehaviorType.qinPalaceEndLevel);
                rst.addRewards(reward1.writeToPb());
                rst.addRewards(reward2.writeToPb());
            }

            qinPalaceModel.clearCurProgress();
            player.getLineupModel().clearRoleTrail(LineupType.QinPalace);
            qinPalaceModel.syncMap();
        }

        player.send(PtCode.QIN_PALACE_END_LEVEL_SERVER, rst.build(), time);
    }

    public static String buildRewardKey(int curLevelId, int curStoryId, int curPointId){
        return curLevelId + "_" + curStoryId + "_" + curPointId;
    }

    /**
     * 选择路线
     */
    @Handler(PtCode.QIN_PALACE_SELECT_WAY_CLIENT)
    private void selectWay(Player player, PbProtocol.QinPalaceSelectWayReq req, long time) {
        PbProtocol.QinPalaceSelectWayRst.Builder rst = PbProtocol.QinPalaceSelectWayRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            int result = beforeCheck(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            PlayerQinPalaceModel qinPalaceModel = player.getModel(PlayerModelEnums.qinPalace);
            if (qinPalaceModel.getCurLevelId() == 0) {
                rst.setResult(Text.genServerRstInfo(Text.还未进入地图));
                break logic;
            }
            int pointId = req.getPointId();
            int wayModelId = qinPalaceModel.getQinPalaceStoreyBean().getWayModelId();
            RgWayModelTemplate rgWayModelTemplate = wayModelTemplateMap.get(wayModelId);
            int curPointId = qinPalaceModel.getCurPointId();
            QinPalaceStoreyBean qinPalaceStoreyBean = qinPalaceModel.getQinPalaceStoreyBean();
            Set<Integer> hasPassPointSet = qinPalaceStoreyBean.getHasPassPointSet();
            if (curPointId == rgWayModelTemplate.lastPointId && hasPassPointSet.contains(curPointId)) {
                rst.setResult(Text.genServerRstInfo(Text.本重已通关请进入下一关));
                break logic;
            }
            boolean isResetDeal = false;
            if (curPointId != 0 && !hasPassPointSet.contains(curPointId)) {
                RgEventType curEventType = qinPalaceModel.getCurEventType();
                //休息时间特殊处理 可以不处理直接跳过
                if (curEventType != RgEventType.rest) {
                    rst.setResult(Text.genServerRstInfo(Text.本关事件还未处理不能选择路线));
                    break logic;
                } else {
                    isResetDeal = true;
                }
            }
            List<Integer> nextPoint;
            // 还在初始站位上
            if (curPointId == 0) {
                nextPoint = rgWayModelTemplate.firstPointIdList;
            } else {
                nextPoint = rgWayModelTemplate.rgMap.get(curPointId);
            }
            if (!nextPoint.contains(pointId)) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            qinPalaceModel.setCurPointId(pointId);
            //休息事件特殊处理 如果直接跳过则置为已完成状态
            if (isResetDeal) {
                hasPassPointSet.add(curPointId);
            }
            // 生成关卡参数
            RgEventType curEventType = qinPalaceModel.getCurEventType();
            curEventType.getPointEventDeal().buildPointParam(player);
            qinPalaceModel.syncMap();
            player.postEvent(PlayerEventType.qinMove);
            //日志
            GameLogger.qinPalaceChooseEvent(player, curEventType, qinPalaceModel.getCurLevelId());
        }

        player.send(PtCode.QIN_PALACE_SELECT_WAY_SERVER, rst.build(), time);
    }

    /**
     * 怪物事件处理
     */
    @Handler(PtCode.QIN_PALACE_EVENT_MONSTER_CLIENT)
    private void eventMonster(Player player, long time) {
        PbProtocol.QinPalaceEventMonsterRst.Builder rst = PbProtocol.QinPalaceEventMonsterRst.newBuilder().setResult(Text.genOkServerRstInfo());
        QinPalaceStage stage = null;
        logic:
        {
            int result = beforeCheck(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            PlayerQinPalaceModel qinPalaceModel = player.getModel(PlayerModelEnums.qinPalace);
            if (qinPalaceModel.getCurLevelId() == 0 || qinPalaceModel.getCurPointId() == 0) {
                rst.setResult(Text.genServerRstInfo(Text.还未进入地图));
                break logic;
            }
            if (!qinPalaceModel.getCurEventType().isMonsterEvent() || qinPalaceModel.isPassCurPointId()) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            result = QinPalaceHelper.checkDisableAndDeadHero(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            List<HeroUnit> heroUnits = player.getLineupModel().createHeroUnits(StageType.qinPalace, LineupType.QinPalace);
            stage = new QinPalaceStage(player, heroUnits, qinPalaceModel.getCurEventParam());
            stage.init();
            rst.setBattleField(stage.getStageRecord());
        }

        player.send(PtCode.QIN_PALACE_EVENT_MONSTER_SERVER, rst.build(), time);
        if (stage != null) {
            CombatManager.combatPrepare(stage);
        }
    }

    /**
     * 休息事件处理
     */
    @Handler(PtCode.QIN_PALACE_EVENT_REST_CLIENT)
    private void eventRest(Player player, PbProtocol.QinPalaceEventResetReq req, long time) {
        PbProtocol.QinPalaceEventResetRst.Builder rst = PbProtocol.QinPalaceEventResetRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            int result = beforeCheck(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            PlayerQinPalaceModel qinPalaceModel = player.getModel(PlayerModelEnums.qinPalace);
            if (qinPalaceModel.getCurLevelId() == 0 || qinPalaceModel.getCurPointId() == 0) {
                rst.setResult(Text.genServerRstInfo(Text.还未进入地图));
                break logic;
            }
            if (qinPalaceModel.getCurEventType() != RgEventType.rest || qinPalaceModel.isPassCurPointId()) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            boolean isWaive = req.getIsWaive();
            if (!isWaive) {
                int eventId = req.getEventId();
                if (!rgConstant.restEventOptList.contains(eventId)) {
                    rst.setResult(Text.genServerRstInfo(Text.参数异常));
                    break logic;
                }
                RgSpecialEventsTemplate rgSpecialEventsTemplate = specialEventsTemplateMap.get(eventId);
                // 处理特殊事件
                PbQinPalace.EventResultInfo.Builder eventBuilder = SpecialEventManager.dealSpecialEvent(player, rgSpecialEventsTemplate);
                rst.addAllHeroId(eventBuilder.getHeroIdList());
                rst.setEventId(eventId);
            }
            rst.setIsWaive(isWaive);
            // 该关卡已通关
            qinPalaceModel.getQinPalaceStoreyBean().getHasPassPointSet().add(qinPalaceModel.getCurPointId());
            qinPalaceModel.syncMap();
            player.postEvent(PlayerEventType.qinRest);
        }
        player.send(PtCode.QIN_PALACE_EVENT_REST_SERVER, rst.build(), time);
    }

    /**
     * 对话事件处理
     */
    @Handler(PtCode.QIN_PALACE_EVENT_CHAT_CLIENT)
    private void eventChat(Player player, PbProtocol.QinPalaceEventChatReq req, long time) {
        PbProtocol.QinPalaceEventChatRst.Builder rst = PbProtocol.QinPalaceEventChatRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            int result = beforeCheck(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            PlayerQinPalaceModel qinPalaceModel = player.getModel(PlayerModelEnums.qinPalace);
            if (qinPalaceModel.getCurLevelId() == 0 || qinPalaceModel.getCurPointId() == 0) {
                rst.setResult(Text.genServerRstInfo(Text.还未进入地图));
                break logic;
            }
            if ((qinPalaceModel.getCurEventType() != RgEventType.chat && qinPalaceModel.getCurEventType() != RgEventType.rest) || qinPalaceModel.isPassCurPointId()) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            int chatId = qinPalaceModel.getCurEventParam();
            RgChatEventsTemplate rgChatEventsTemplate = chatEventsTemplateMap.get(chatId);
            if(Objects.isNull(rgChatEventsTemplate)){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            int select = req.getSelect();
            if (select <= 0 || select > rgChatEventsTemplate.parameterList.size()) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            //检查条件是否满足
            if(!rgChatEventsTemplate.optionCanTrigger(player, select - 1)){
                rst.setResult(Text.genServerRstInfo(Text.秦皇地宫事件选项条件不满足));
                break logic;
            }
            String specialIds = rgChatEventsTemplate.parameterList.get(select - 1);
            if(specialIds.contains("&")){
                String[] split = specialIds.split("&");
                for (String s : split) {
                    rst.addEventResults(eventOptionTrigger(player, qinPalaceModel, Integer.parseInt(s)));
                }
            }else if(specialIds.contains("|")){
                Map<Integer, Integer> specialInfos = new HashMap<>();
                String[] split = specialIds.split("|");
                for (String s : split) {
                    String[] split1 = s.split(",");
                    specialInfos.put(Integer.parseInt(split1[0]), Integer.parseInt(split1[1]));
                }
                int specialId = MathUtil.weightRandom(specialInfos);
                rst.addEventResults(eventOptionTrigger(player, qinPalaceModel, specialId));
            }else{
                int specialId = Integer.parseInt(specialIds);
                rst.addEventResults(eventOptionTrigger(player, qinPalaceModel, specialId));
            }
            qinPalaceModel.syncMap();
            player.postEvent(PlayerEventType.qinChat, chatId, select);
        }
        player.send(PtCode.QIN_PALACE_EVENT_CHAT_SERVER, rst.build(), time);
    }

    private PbQinPalace.EventResultInfo.Builder eventOptionTrigger(Player player, PlayerQinPalaceModel qinPalaceModel, int specialId){
        RgSpecialEventsTemplate rgSpecialEventsTemplate = specialEventsTemplateMap.get(specialId);
        // 处理特殊事件
        PbQinPalace.EventResultInfo.Builder eventBuilder = SpecialEventManager.dealSpecialEvent(player, rgSpecialEventsTemplate);
        //除了战斗事件其他都设置为已通关
        if (rgSpecialEventsTemplate.eventType != RgSpecialEventsType.fight) {
            qinPalaceModel.getQinPalaceStoreyBean().getHasPassPointSet().add(qinPalaceModel.getCurPointId());
        }
        //记录已经触发过的特殊事件
        qinPalaceModel.getRecordSpecialIds().add(specialId);

        return eventBuilder;
    }

    /**
     * 进入下一层
     */
    @Handler(PtCode.QIN_PALACE_NEXT_STOREY_CLIENT)
    private void nextStorey(Player player, long time) {
        PbProtocol.QinPalaceNextStoreyRst.Builder rst = PbProtocol.QinPalaceNextStoreyRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            int result = beforeCheck(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            PlayerQinPalaceModel qinPalaceModel = player.getModel(PlayerModelEnums.qinPalace);
            if (qinPalaceModel.getCurLevelId() == 0) {
                rst.setResult(Text.genServerRstInfo(Text.还未进入地图));
                break logic;
            }
            // 没有通关当前重 或者 已经通关整个层了
            if (!qinPalaceModel.isPassCurStorey() || qinPalaceModel.isLastStoreyId()) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            //检查神器是否有挑战开始选择神器
            if(qinPalaceModel.hasAddedEquip()){
                RgStoreyTemplate rgStoreyTemplate = QinPalaceRgService.getRgStoreyTemplate(qinPalaceModel.getCurLevelId(), qinPalaceModel.getCurStoryId());
                if(Objects.nonNull(rgStoreyTemplate)){
                    Integer equipDropId = rgStoreyTemplate.equipDrop.get(qinPalaceModel.getCurEventType());
                    RgEquipDropTemplate rgEquipDropTemplate = QinPalaceRgService.getEquipDropTemplateMap().get(equipDropId);
                    Set<Integer> dropRgEquip = QinPalaceHelper.dropRgEquip(qinPalaceModel, rgEquipDropTemplate);
                    qinPalaceModel.getToChooseEquipIdSet().clear();
                    qinPalaceModel.getToChooseEquipIdSet().addAll(dropRgEquip);
                }
            }
            RgStoreyTemplate rgStoreyTemplate = getRgStoreyTemplate(qinPalaceModel.getCurLevelId(), qinPalaceModel.getCurStoryId());
            List<Reward> rewards = Reward.addFromTemplates(rgStoreyTemplate.firstPassReward, player, BehaviorType.qinPalaceNextStory);
            rst.addAllRewards(Reward.writeCollectionToPb(rewards));
            // 去下一重
            qinPalaceModel.jumpNextStorey();
            qinPalaceModel.syncMap();

        }
        player.send(PtCode.QIN_PALACE_NEXT_STOREY_SERVER, rst.build(), time);
    }

    /**
     * 三选一神器
     */
    @Handler(PtCode.QIN_PALACE_CHOOSE_EQUIP_CLIENT)
    private void chooseEquip(Player player, PbProtocol.QinPalaceChooseEquipReq req, long time) {
        PbProtocol.QinPalaceChooseEquipRst.Builder rst = PbProtocol.QinPalaceChooseEquipRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            int result = beforeCheck(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            PlayerQinPalaceModel qinPalaceModel = player.getModel(PlayerModelEnums.qinPalace);
            if (qinPalaceModel.getCurLevelId() == 0 || qinPalaceModel.getCurPointId() == 0) {
                rst.setResult(Text.genServerRstInfo(Text.还未进入地图));
                break logic;
            }
            if (!qinPalaceModel.getCurEventType().isMonsterEvent()) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            // 不是处理中状态
            if (QinPalaceHelper.getCurEventState(player) != RgEventState.dealing) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            int equipId = req.getEquipId();
            int optType = req.getOptType();
            int index = req.getEquipIndex();
            switch (optType) {
                case 1: {//穿戴神器
                    if(index < 0){
                        //背包满了
                        if(qinPalaceModel.getBagEquipMap().size() < rgConstant.wearEquipMax){
                            rst.setResult(Text.genServerRstInfo(Text.参数异常));
                        }
                    }else{
                        if (index >= rgConstant.wearEquipMax) {
                            rst.setResult(Text.genServerRstInfo(Text.参数异常));
                            break logic;
                        }
                        if (qinPalaceModel.getBagEquipMap().containsKey(index)) {
                            rst.setResult(Text.genServerRstInfo(Text.参数异常));
                            break logic;
                        }
                    }
                    if (!qinPalaceModel.getToChooseEquipIdSet().contains(equipId)) {
                        rst.setResult(Text.genServerRstInfo(Text.参数异常));
                        break logic;
                    }
                    RgEquipTemplate equipTemplate = equipTemplateMap.get(equipId);
                    // 回调事件不为空
                    if (equipTemplate.backEvent != -1) {
                        SpecialEventManager.dealSpecialEvent(player, equipTemplate.backEvent);
                    }
                    int oldEquipId = -1;
                    QinPalaceGlobalData qinPalaceGlobalData = GlobalDataManager.getData(GlobalDataType.QinPalace);
                    qinPalaceModel.addEquip(equipId, index);
                    int roundId = qinPalaceGlobalData.getSeasonId();
                    //日志
                    GameLogger.qinPalaceChooseEquip(player, equipId, oldEquipId, roundId, qinPalaceModel.getCurLevelId());
                    break;
                }
                case 2: {//放弃不作处理
                    // 神器没满不能放弃
                    if (qinPalaceModel.getBagEquipMap().size() < rgConstant.wearEquipMax) {
                        rst.setResult(Text.genServerRstInfo(Text.参数异常));
                        break logic;
                    }
                    break;
                }
                default: {
                    rst.setResult(Text.genServerRstInfo(Text.参数异常));
                    break logic;
                }
            }
            // 清空三选一神器列表
            qinPalaceModel.getToChooseEquipIdSet().clear();
            // 已完成关卡记录
            qinPalaceModel.getQinPalaceStoreyBean().getHasPassPointSet().add(qinPalaceModel.getCurPointId());
            // 本重通关
            if (qinPalaceModel.isPassCurStorey()) {
                RgStoreyTemplate rgStoreyTemplate = QinPalaceRgService.getRgStoreyTemplate(qinPalaceModel.getCurLevelId(), qinPalaceModel.getCurStoryId());
                if (Objects.nonNull(rgStoreyTemplate)) {
                    qinPalaceModel.getPassStoryIdSet().add(rgStoreyTemplate.id);
                }
                // 本层已经通关了
                if (qinPalaceModel.isLastStoreyId()) {
                    qinPalaceModel.getPassLevelIdSet().add(qinPalaceModel.getCurLevelId());

                    //发送通关本层事件
                    player.postEvent(PlayerEventType.qinPassLevel, qinPalaceModel.getCurLevelId(), qinPalaceModel.getCurStoryId());

                    // 关卡重置 回主界面
                    qinPalaceModel.clearCurProgress();
                }
            }
            qinPalaceModel.addEquipGuide(equipId);
            RedDot.qinPalaceEquipReward.sync(player);
            qinPalaceModel.syncMap();
            rst.setEquipId(equipId)
                    .setEquipIndex(index)
                    .setOptType(optType);
            player.postEvent(PlayerEventType.qinEquip, equipId);


        }
        player.send(PtCode.QIN_PALACE_CHOOSE_EQUIP_SERVER, rst.build(), time);
    }

    /**
     * 领取阶段奖励
     */
    @Handler(PtCode.QIN_PALACE_DRAW_REWARD_CLIENT)
    private void drawReward(Player player, PbProtocol.QinPalaceDrawRewardReq req, long time) {
        PbProtocol.QinPalaceDrawRewardRst.Builder rst = PbProtocol.QinPalaceDrawRewardRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            int result = beforeCheck(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            int rewardId = req.getRewardId();
            Map<Integer, RgRewardTemplate> rewardTemplateMap = QinPalaceRgService.getRewardTemplateMap();
            RgRewardTemplate rgRewardTemplate = rewardTemplateMap.get(rewardId);
            if (Objects.isNull(rgRewardTemplate)) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            if(rgRewardTemplate.goalId > 0 && !player.getGoalModel().isFinish(rgRewardTemplate.goalId)){
                rst.setResult(Text.genServerRstInfo(Text.还未通关该重不可以领取奖励));
                break logic;
            }
            PlayerQinPalaceModel qinPalaceModel = player.getModel(PlayerModelEnums.qinPalace);
            Set<Integer> hasRewardIdSet = qinPalaceModel.getHasRewardIdSet();
            if (hasRewardIdSet.contains(rewardId)) {
                rst.setResult(Text.genServerRstInfo(Text.关卡奖励已领取));
                break logic;
            }

            hasRewardIdSet.add(rewardId);

            List<Reward> rewards = Reward.templateCollectionToReward(rgRewardTemplate.reward);
            //特权
            LibertyHelper.checkCurrencyRewardLiberty(LibertyType.qinPalaceRate, player, rewards);
            Reward.add(rewards, player, BehaviorType.qinPalaceReward);
//            List<Reward> rewards = Reward.addFromTemplates(rgRewardTemplate.reward, player, BehaviorType.qinPalaceReward);
            rst.addAllReward(Reward.writeCollectionToPb(rewards));
            rst.addAllHasRewardId(hasRewardIdSet);
        }
        player.send(PtCode.QIN_PALACE_DRAW_REWARD_SERVER, rst.build(), time);
    }

    /**
     * 天赋点重置
     */
    @Handler(PtCode.QIN_PALACE_TALENT_RESET_CLIENT)
    private void talentReset(Player player, long time) {
        PbProtocol.QinPalaceTalentResetRst.Builder rst = PbProtocol.QinPalaceTalentResetRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            int result = beforeCheck(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            PlayerQinPalaceModel qinPalaceModel = player.getModel(PlayerModelEnums.qinPalace);
            Map<Integer, QinTalentInfo> talentMap = qinPalaceModel.getTalentMap();
            if (talentMap.isEmpty()) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            Map<Integer, RgTalentTemplate> talentTempMap = getTalentTemplateMap();
            List<RewardTemplate> returnRewardList = new ArrayList<>();
            for (Map.Entry<Integer, QinTalentInfo> entry : talentMap.entrySet()) {
                Integer talentId = entry.getKey();
                QinTalentInfo talentInfo = entry.getValue();
                int talentLv = talentInfo.getTalentLv();
                RgTalentTemplate rgTalentTemplate = talentTempMap.get(talentId);
                returnRewardList.addAll(rgTalentTemplate.talentUpUseCurrencyNum);
            }
            talentMap.clear();
            List<Reward> rewardList = Reward.addFromTemplates(returnRewardList, player, BehaviorType.qinPalaceTalentReset);
            rst.addAllReward(Reward.writeCollectionToPb(rewardList));
            //日志
            GameLogger.qinPalaceTalentUp(player, -1, true);
        }
        player.send(PtCode.QIN_PALACE_TALENT_RESET_SERVER, rst.build(), time);
    }


    /**
     * 更改装备神器
     */
    @Handler(PtCode.QIN_PALACE_UPDATE_EQUIP_REQ)
    private void updateEquip(Player player, PbProtocol.QinPalaceUpdateEquipReq req, long time) {
        int equipId = req.getEquipId();
        int optType = req.getOptType();
        int index = req.getIndex();
        PbProtocol.QinPalaceUpdateEquipRst.Builder rst = PbProtocol.QinPalaceUpdateEquipRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            int result = beforeCheck(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            PlayerQinPalaceModel qinPalaceModel = player.getModel(PlayerModelEnums.qinPalace);
            switch (optType) {
                case 1:{
                    //穿
                    if(!qinPalaceModel.getEquipBagExtends().containsKey(equipId)){
                        rst.setResult(Text.genServerRstInfo(Text.秦皇地宫神器不存在));
                        break logic;
                    }

                    //开始处理穿神器
                    //检查旧位置是否有神器，先脱了
                    if (qinPalaceModel.getBagEquipMap().containsKey(index)) {
                        QinEquipInfo oldEquipInfo = qinPalaceModel.getBagEquipMap().get(index);
                        //旧神器放进背包
                        qinPalaceModel.getEquipBagExtends().put(oldEquipInfo.getId(), oldEquipInfo);
                    }
                    QinEquipInfo qinEquipInfo = qinPalaceModel.getEquipBagExtends().get(equipId);
                    qinPalaceModel.getBagEquipMap().put(index, qinEquipInfo);
                    qinPalaceModel.getEquipBagExtends().remove(equipId);
                    break;
                }
                case 2:{
                    //卸
                    if(!qinPalaceModel.getBagEquipMap().containsKey(index)){
                        rst.setResult(Text.genServerRstInfo(Text.当前位置神器为空不能卸下));
                        break logic;
                    }

                    //开始处理脱神器
                    QinEquipInfo oldEquipInfo = qinPalaceModel.getBagEquipMap().get(index);
                    //旧神器放进背包
                    qinPalaceModel.getEquipBagExtends().put(oldEquipInfo.getId(), oldEquipInfo);
                    qinPalaceModel.getBagEquipMap().remove(index);
                    break;
                }
                default:{
                    rst.setResult(Text.genServerRstInfo(Text.参数异常));
                    break;
                }
            }
            rst.setEquipId(equipId);
            rst.setIndex(index);
            rst.setOptType(optType);
        }
        player.send(PtCode.QIN_PALACE_UPDATE_EQUIP_RST, rst.build(), time);
    }

    /**
     * 领取秦皇地宫神器图鉴奖励
     */
    @Handler(PtCode.QIN_PALACE_EQUIP_GUIDE_REWARD_REQ)
    private void equipGuideReward(Player player, PbProtocol.QinPalaceEquipGuideRewardReq req, long time) {
        PbProtocol.QinPalaceEquipGuideRewardRst.Builder rst = PbProtocol.QinPalaceEquipGuideRewardRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            int equipId = req.getEquipId();
            PlayerQinPalaceModel qinPalaceModel = player.getModel(PlayerModelEnums.qinPalace);
            List<Reward> rewardList = new ArrayList<>();
            if(equipId > 0){
                if(!qinPalaceModel.getEquipGuides().containsKey(equipId)){
                    rst.setResult(Text.genServerRstInfo(Text.秦皇地宫神器图鉴未解锁));
                    break logic;
                }
                if(qinPalaceModel.getEquipGuides().get(equipId)){
                    rst.setResult(Text.genServerRstInfo(Text.秦皇地宫神器图鉴已经领取奖励));
                    break logic;
                }
                receiveEquipGuide(qinPalaceModel, equipId, rewardList);
            }else{
                Map<Integer, Boolean> equipGuides = qinPalaceModel.getEquipGuides();
                List<Integer> equipGuideIds = new ArrayList<>(equipGuides.keySet());
                for (Integer equipGuideId : equipGuideIds) {
                    if(!equipGuides.get(equipGuideId)){
                        receiveEquipGuide(qinPalaceModel, equipGuideId, rewardList);
                    }
                }
            }
            if(CollectionUtil.isNotEmpty(rewardList)){
                Reward.merge(rewardList);
                Reward.add(rewardList, player, BehaviorType.qinPalaceEquipGuide);
                rst.addAllReward(Reward.writeCollectionToPb(rewardList));
            }
            rst.setEquipId(equipId);
            rst.putAllEquipGuides(qinPalaceModel.getEquipGuides());
            RedDot.qinPalaceEquipReward.sync(player);
        }
        player.send(PtCode.QIN_PALACE_EQUIP_GUIDE_REWARD_RST, rst.build(), time);
    }

    private static void receiveEquipGuide(PlayerQinPalaceModel qinPalaceModel, int equipId, List<Reward> rewardList){
        qinPalaceModel.getEquipGuides().put(equipId, true);
        //增加奖励
        RgEquipTemplate rgEquipTemplate = equipTemplateMap.get(equipId);
        rewardList.addAll(Reward.templateCollectionToReward(rgEquipTemplate.guideRewards));
    }

    /**
     * 神器合成
     */
    @Handler(PtCode.QIN_PALACE_EQUIP_COMPOUND_REQ)
    private void equipCompound(Player player, PbProtocol.QinPalaceEquipCompoundReq req, long time) {
        PbProtocol.QinPalaceEquipCompoundRst.Builder rst = PbProtocol.QinPalaceEquipCompoundRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            Set<Integer> costEquipIdsList = new HashSet<>(req.getCostEquipIdsList());
            int targetEquipId = req.getTargetEquipId();
            if(costEquipIdsList.size() <= 1 || costEquipIdsList.size() > 3){
                rst.setResult(Text.genServerRstInfo(Text.秦皇地宫神器合成消耗为空));
                break logic;
            }
            PlayerQinPalaceModel qinPalaceModel = player.getModel(PlayerModelEnums.qinPalace);
            for (int equipId : costEquipIdsList) {
                if(!qinPalaceModel.getEquipBagExtends().containsKey(equipId)
                    && qinPalaceModel.getBagEquipIndex(equipId) < 0){
                    rst.setResult(Text.genServerRstInfo(Text.秦皇地宫神器不存在));
                    break logic;
                }
            }
            if(targetEquipId > 0){
                //指定合成
                if(qinPalaceModel.getEquipBagExtends().containsKey(targetEquipId)){
                    rst.setResult(Text.genServerRstInfo(Text.秦皇地宫不能获取重复神器));
                    break logic;
                }
                for (QinEquipInfo qinEquipInfo : qinPalaceModel.getBagEquipMap().values()) {
                    if(qinEquipInfo.getId() == targetEquipId){
                        rst.setResult(Text.genServerRstInfo(Text.秦皇地宫不能获取重复神器));
                        break logic;
                    }
                }
                //检查材料是否符合
                RgEquipTemplate rgEquipTemplate = equipTemplateMap.get(targetEquipId);
                if(rgEquipTemplate.CompoundEquips.size() != costEquipIdsList.size()){
                    rst.setResult(Text.genServerRstInfo(Text.秦皇地宫神器合成消耗错误));
                    break logic;
                }
                for (Integer costEquipId : costEquipIdsList) {
                    if(!rgEquipTemplate.CompoundEquips.contains(costEquipId)){
                        rst.setResult(Text.genServerRstInfo(Text.秦皇地宫神器合成消耗错误));
                        break logic;
                    }
                }
                //删除消耗
                for (Integer costEquipId : costEquipIdsList) {
                    if(qinPalaceModel.getEquipBagExtends().containsKey(costEquipId)){
                        qinPalaceModel.getEquipBagExtends().remove(costEquipId);
                    }else{
                        qinPalaceModel.getBagEquipMap().remove(qinPalaceModel.getBagEquipIndex(costEquipId));
                    }
                }

            }else{
                //随机合成
                //根据品质随机新品质，然后随机
                Map<Integer, Integer> integerIntegerMap = calQualityAndProbability(costEquipIdsList);
                int newQuality = MathUtil.weightRandom(integerIntegerMap);
                List<Integer> sameQualityEquipIds = equipQualityInfoMap.get(newQuality);
                for (Integer equipId : qinPalaceModel.getEquipBagExtends().keySet()) {
                    sameQualityEquipIds.remove(equipId);
                }
                for (QinEquipInfo qinEquipInfo : qinPalaceModel.getBagEquipMap().values()) {
                    if (sameQualityEquipIds.contains(qinEquipInfo.getId())) {
                        sameQualityEquipIds.remove((Integer)qinEquipInfo.getId());
                    }
                }
                if(CollectionUtil.isEmpty(sameQualityEquipIds)){
                    rst.setResult(Text.genServerRstInfo(Text.秦皇地宫该品质神器已满));
                    break logic;
                }
                //删除消耗
                for (Integer costEquipId : costEquipIdsList) {
                    if(qinPalaceModel.getEquipBagExtends().containsKey(costEquipId)){
                        qinPalaceModel.getEquipBagExtends().remove(costEquipId);
                    }else{
                        qinPalaceModel.getBagEquipMap().remove(qinPalaceModel.getBagEquipIndex(costEquipId));
                    }
                }
                Collections.shuffle(sameQualityEquipIds);
                targetEquipId = sameQualityEquipIds.get(0);
            }

            //掉落神器
            qinPalaceModel.addEquip(targetEquipId);

            rst.setNewEquipId(targetEquipId);

            qinPalaceModel.addEquipGuide(rst.getNewEquipId());
            RedDot.qinPalaceEquipReward.sync(player);
            rst.addAllCostEquipIds(costEquipIdsList);
            rst.addAllEquipBagExtends(qinPalaceModel.getEquipBagExtends().keySet());
            for (Map.Entry<Integer, QinEquipInfo> entry : qinPalaceModel.getBagEquipMap().entrySet()) {
                rst.putBagEquipId(entry.getKey(), entry.getValue().getId());
            }
        }
        player.send(PtCode.QIN_PALACE_EQUIP_COMPOUND_RST, rst.build(), time);
    }

    private Map<Integer, Integer> calQualityAndProbability(Set<Integer> equipIds){
        Map<Integer, Integer> result = new HashMap<>();
        int purpleQuality = 0;
        int goldQuality = 0;
        RgConstant rgConstant = getRgConstant();
        for (Integer equipId : equipIds) {
            RgEquipTemplate rgEquipTemplate = equipTemplateMap.get(equipId);
            String[] split;
            if(rgEquipTemplate.equipQuality == 3){
                //蓝色
                split = rgConstant.BlueArtifactProbabilityIncrease.split(",");
            }else if(rgEquipTemplate.equipQuality == 2){
                //紫色
                split = rgConstant.PurpleArtifactProbabilityIncrease.split(",");
            }else{
                //橙色
                split = rgConstant.GlodenArtifactProbabilityIncrease.split(",");
            }
            purpleQuality += Integer.parseInt(split[0]);
            goldQuality += Integer.parseInt(split[1]);
        }
        goldQuality = Math.min(goldQuality, 100);
        result.put(1, goldQuality);
        purpleQuality = Math.min(purpleQuality, 100 - goldQuality);
        result.put(2, purpleQuality);
        int blueQuality = Math.max(0, 100 - goldQuality - purpleQuality);
        result.put(3, blueQuality);
        return result;
    }

    /**
     * 刷新神器三选一
     */
    @Handler(PtCode.QIN_PALACE_REFRESH_CHOOSE_EQUIP_REQ)
    private void refreshEquip(Player player, PbProtocol.QinPalaceRefreshChooseEquipReq req, long time) {
        PbProtocol.QinPalaceRefreshChooseEquipRst.Builder rst = PbProtocol.QinPalaceRefreshChooseEquipRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            PlayerQinPalaceModel qinPalaceModel = player.getModel(PlayerModelEnums.qinPalace);
            if (qinPalaceModel.getCurLevelId() == 0 || qinPalaceModel.getCurPointId() == 0) {
                rst.setResult(Text.genServerRstInfo(Text.还未进入地图));
                break logic;
            }
            if (!qinPalaceModel.getCurEventType().isMonsterEvent()) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            // 不是处理中状态
            if (QinPalaceHelper.getCurEventState(player) != RgEventState.dealing) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            if(CollectionUtil.isEmpty(qinPalaceModel.getToChooseEquipIdSet())){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            //检测刷新消耗，并且扣除
            Reward cost = getRgConstant().ArtifactRefreshCost.createReward();
            if(cost.check(player) != -1){
                rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                break logic;
            }
            cost.remove(player, BehaviorType.qinPalaceEquipRefresh);


            RgStoreyTemplate rgStoreyTemplate = QinPalaceRgService.getRgStoreyTemplate(qinPalaceModel.getCurLevelId(), qinPalaceModel.getCurStoryId());
            if(Objects.nonNull(rgStoreyTemplate)){
                Integer equipDropId = rgStoreyTemplate.equipDrop.get(qinPalaceModel.getCurEventType());
                RgEquipDropTemplate rgEquipDropTemplate = QinPalaceRgService.getEquipDropTemplateMap().get(equipDropId);
                Set<Integer> dropRgEquip = QinPalaceHelper.dropRgEquip(qinPalaceModel, rgEquipDropTemplate);
                qinPalaceModel.getToChooseEquipIdSet().clear();
                qinPalaceModel.getToChooseEquipIdSet().addAll(dropRgEquip);
            }
            rst.addAllToChooseEquipIdSet(qinPalaceModel.getToChooseEquipIdSet());
        }
        player.send(PtCode.QIN_PALACE_REFRESH_CHOOSE_EQUIP_RST, rst.build(), time);
    }

    /**
     * 事件商店购买
     */
    @Handler(PtCode.QIN_PALACE_EVENT_SHOP_BUY_REQ)
    private void eventShopBuy(Player player, PbProtocol.QinPalaceEventShopBuyReq req, long time) {
        PbProtocol.QinPalaceEventShopBuyRst.Builder rst = PbProtocol.QinPalaceEventShopBuyRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            int equipId = req.getEquipId();
            PlayerQinPalaceModel qinPalaceModel = player.getModel(PlayerModelEnums.qinPalace);
            if (qinPalaceModel.getCurLevelId() == 0 || qinPalaceModel.getCurPointId() == 0) {
                rst.setResult(Text.genServerRstInfo(Text.还未进入地图));
                break logic;
            }
            RgEquipTemplate rgEquipTemplate = getEquipTemplateMap().get(equipId);
            //检查神器能否掉落
            for (QinEquipInfo equipInfo : qinPalaceModel.getBagEquipMap().values()) {
                if(equipInfo.getId() == equipId
                        || rgEquipTemplate.CompoundEquips.contains(equipInfo.getId())){
                    rst.setResult(Text.genServerRstInfo(Text.秦皇地宫神器不能选择));
                    break logic;
                }
            }
            for (QinEquipInfo equipInfo : qinPalaceModel.getEquipBagExtends().values()) {
                if(equipInfo.getId() == equipId
                        || rgEquipTemplate.CompoundEquips.contains(equipInfo.getId())){
                    rst.setResult(Text.genServerRstInfo(Text.秦皇地宫神器不能选择));
                    break logic;
                }
            }
            if(CollectionUtil.isEmpty(qinPalaceModel.getStoreEventIds())){
                rst.setResult(Text.genServerRstInfo(Text.秦皇地宫事件商店不存在));
                break logic;
            }
            if(!qinPalaceModel.getStoreEventIds().containsKey(equipId) || !equipTemplateMap.containsKey(equipId)){
                rst.setResult(Text.genServerRstInfo(Text.秦皇地宫事件商品不存在));
                break logic;
            }
            Reward reward = rgEquipTemplate.costs.createReward();
            if(reward.check(player) != -1){
                rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                break logic;
            }
            reward.remove(player, BehaviorType.qinPalaceEventShopBuy);
            qinPalaceModel.getStoreEventIds().put(equipId, true);
            qinPalaceModel.addEquip(equipId);
            rst.putAllShopEquipIds(qinPalaceModel.getStoreEventIds());
        }
        player.send(PtCode.QIN_PALACE_EVENT_SHOP_BUY_RST, rst.build(), time);
    }

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        // 天赋表
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.usual_QinPalace_rgTalent);
        Map<Integer, Map<Integer, RgTalentTemplate>> talentTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            RgTalentTemplate rgTalentTemplate = new RgTalentTemplate(map);
            if(!talentTemplateMapTemp.containsKey(rgTalentTemplate.templateId)){
                talentTemplateMapTemp.put(rgTalentTemplate.templateId, new HashMap<>());
            }
            talentTemplateMapTemp.get(rgTalentTemplate.templateId).put(rgTalentTemplate.talentId, rgTalentTemplate);
        }
        talentTemplateMap = talentTemplateMapTemp;

        // 奖励表
        mapList = ConfigReader.read(ConfigFile.usual_QinPalace_rgReward);
        Map<Integer, Map<Integer, RgRewardTemplate>> rewardTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            RgRewardTemplate rgRewardTemplate = new RgRewardTemplate(map);
            if(!rewardTemplateMapTemp.containsKey(rgRewardTemplate.templateId)){
                rewardTemplateMapTemp.put(rgRewardTemplate.templateId, new HashMap<>());
            }
            rewardTemplateMapTemp.get(rgRewardTemplate.templateId).put(rgRewardTemplate.id, rgRewardTemplate);
        }
        rewardTemplateMap = rewardTemplateMapTemp;

        // 常量表
        Map<String, String> constantMap = ConstantConfigReader.read(ConfigFile.usual_QinPalace_constant);
        rgConstant = new RgConstant(constantMap);

        // 关卡路线表
        mapList = ConfigReader.read(ConfigFile.usual_QinPalace_rgWayModel);
        Map<Integer, RgWayModelTemplate> wayModelTemplateMapTemp = new HashMap<>();
        List<RgWayModelTemplate> randomWayModelTemplateListTemp = new ArrayList<>();
        for (Map<String, String> map : mapList) {
            RgWayModelTemplate rgWayModelTemplate = new RgWayModelTemplate(map);
            wayModelTemplateMapTemp.put(rgWayModelTemplate.id, rgWayModelTemplate);
            if (rgWayModelTemplate.isRo) {
                randomWayModelTemplateListTemp.add(rgWayModelTemplate);
            }
        }
        wayModelTemplateMap = wayModelTemplateMapTemp;
        randomWayModelTemplateList = randomWayModelTemplateListTemp;

        // 关卡表
        mapList = ConfigReader.read(ConfigFile.usual_QinPalace_rgStorey);
        Map<Integer, Map<Integer, Map<Integer, RgStoreyTemplate>>> storeyTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            RgStoreyTemplate rgStoreyTemplate = new RgStoreyTemplate(map);
            Map<Integer, Map<Integer, RgStoreyTemplate>> templateMap = storeyTemplateMapTemp.computeIfAbsent(rgStoreyTemplate.templateId, map1 -> new HashMap<>());
            Map<Integer, RgStoreyTemplate> templateMap1 = templateMap.computeIfAbsent(rgStoreyTemplate.levelId, map1 -> new HashMap<>());
            templateMap1.put(rgStoreyTemplate.storeyId, rgStoreyTemplate);
        }
        storeyTemplateMap = storeyTemplateMapTemp;

        // 神器掉落表
        mapList = ConfigReader.read(ConfigFile.usual_QinPalace_rgEquipDrop);
        Map<Integer, RgEquipDropTemplate> equipDropTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            RgEquipDropTemplate rgEquipDropTemplate = new RgEquipDropTemplate(map);
            equipDropTemplateMapTemp.put(rgEquipDropTemplate.id, rgEquipDropTemplate);
        }
        equipDropTemplateMap = equipDropTemplateMapTemp;

        // 怪物站位表
        mapList = ConfigReader.read(ConfigFile.usual_QinPalace_rgMonsterGroup);
        Map<Integer, RgMonsterGroupTemplate> monsterGroupTemplateMapTemp = new HashMap<>();
        Map<RgEventType, List<RgMonsterGroupTemplate>> monsterGroupTemplateByTypeMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            RgMonsterGroupTemplate rgMonsterGroupTemplate = new RgMonsterGroupTemplate(map);
            monsterGroupTemplateMapTemp.put(rgMonsterGroupTemplate.id, rgMonsterGroupTemplate);
            List<RgMonsterGroupTemplate> list = monsterGroupTemplateByTypeMapTemp.computeIfAbsent(rgMonsterGroupTemplate.monsterType, list1 -> new ArrayList<>());
            list.add(rgMonsterGroupTemplate);
        }
        monsterGroupTemplateMap = monsterGroupTemplateMapTemp;
        monsterGroupTemplateByTypeMap = monsterGroupTemplateByTypeMapTemp;

        // 对话事件表
        mapList = ConfigReader.read(ConfigFile.usual_QinPalace_rgChatEvents);
        Map<Integer, RgChatEventsTemplate> chatEventsTemplateMapTemp = new HashMap<>();
        Map<RgChatEventsTemplate, Integer> chatEventsWeightMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            RgChatEventsTemplate rgChatEventsTemplate = new RgChatEventsTemplate(map);
            chatEventsTemplateMapTemp.put(rgChatEventsTemplate.id, rgChatEventsTemplate);
            chatEventsWeightMapTemp.put(rgChatEventsTemplate, rgChatEventsTemplate.weight);
        }
        chatEventsTemplateMap = chatEventsTemplateMapTemp;
        chatEventsWeightMap = chatEventsWeightMapTemp;


        // 特殊事件表
        mapList = ConfigReader.read(ConfigFile.usual_QinPalace_rgSpecialEvents);
        Map<Integer, RgSpecialEventsTemplate> specialEventsTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            RgSpecialEventsTemplate rgSpecialEventsTemplate = new RgSpecialEventsTemplate(map);
            specialEventsTemplateMapTemp.put(rgSpecialEventsTemplate.eventId, rgSpecialEventsTemplate);
            if (rgSpecialEventsTemplate.eventType == RgSpecialEventsType.nothing) {
                nothingSpecialEventId = rgSpecialEventsTemplate.eventId;
            }
        }
        specialEventsTemplateMap = specialEventsTemplateMapTemp;

        // 神器表
        mapList = ConfigReader.read(ConfigFile.usual_QinPalace_rgEquip);
        Map<Integer, RgEquipTemplate> equipTemplateMapTemp = new HashMap<>();
        Map<Integer, List<Integer>> equipQualityInfoMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            RgEquipTemplate rgEquipTemplate = new RgEquipTemplate(map);
            equipTemplateMapTemp.put(rgEquipTemplate.id, rgEquipTemplate);
            if(!equipQualityInfoMapTemp.containsKey(rgEquipTemplate.equipQuality)){
                equipQualityInfoMapTemp.put(rgEquipTemplate.equipQuality, new ArrayList<>());
            }
            equipQualityInfoMapTemp.get(rgEquipTemplate.equipQuality).add(rgEquipTemplate.id);
        }
        equipTemplateMap = equipTemplateMapTemp;
        equipQualityInfoMap = equipQualityInfoMapTemp;

        // 诅咒BUFF表
        mapList = ConfigReader.read(ConfigFile.usual_QinPalace_rgDamnation);
        Map<Integer, RgDamnationTemplate> damnationTemplateMapTemp = new HashMap<>();
        Map<RgDamnationTemplate, Integer> damnationTemplateWeightMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            RgDamnationTemplate rgDamnationTemplate = new RgDamnationTemplate(map);
            damnationTemplateMapTemp.put(rgDamnationTemplate.id, rgDamnationTemplate);
            damnationTemplateWeightMapTemp.put(rgDamnationTemplate, rgDamnationTemplate.weight);
        }
        damnationTemplateMap = damnationTemplateMapTemp;
        damnationTemplateWeightMap = damnationTemplateWeightMapTemp;

        // 感染Buff表
        mapList = ConfigReader.read(ConfigFile.usual_QinPalace_rgInfect);
        Map<Integer, RgInfectTemplate> infectTemplateMapTemp = new HashMap<>();
        Map<RgInfectTemplate, Integer> infectTemplateWeightMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            RgInfectTemplate rgInfectTemplate = new RgInfectTemplate(map);
            infectTemplateMapTemp.put(rgInfectTemplate.id, rgInfectTemplate);
            infectTemplateWeightMapTemp.put(rgInfectTemplate, rgInfectTemplate.weight);
        }
        infectTemplateMap = infectTemplateMapTemp;
        infectTemplateWeightMap = infectTemplateWeightMapTemp;

        // 怪物属性成长表
        mapList = ConfigReader.read(ConfigFile.usual_QinPalace_monsterGrowthCoe);
        Table<Integer, Integer, Map<Integer, RgMonsterGrowthCoeTemplate>> worldLevelAttTemplateTableTemp = HashBasedTable.create();
        for (Map<String, String> map : mapList) {
            RgMonsterGrowthCoeTemplate growthCoeTemplate = new RgMonsterGrowthCoeTemplate(map);
            Map<Integer, RgMonsterGrowthCoeTemplate> templateMap = worldLevelAttTemplateTableTemp.get(growthCoeTemplate.levelId, growthCoeTemplate.storeyId);
            if (templateMap == null) {
                templateMap = new HashMap<>();
                worldLevelAttTemplateTableTemp.put(growthCoeTemplate.levelId, growthCoeTemplate.storeyId, templateMap);
            }
            templateMap.put(growthCoeTemplate.pointId, growthCoeTemplate);
        }
        worldLevelAttTemplateTable = worldLevelAttTemplateTableTemp;

        mapList = ConfigReader.read(ConfigFile.usual_QinPalace_rgSeason);
        Map<Integer, RgSeasonTemplate> seasonTemplates = new HashMap<>();
        for (Map<String, String> map : mapList) {
            RgSeasonTemplate seasonTemplate = new RgSeasonTemplate(map);
            seasonTemplates.put(seasonTemplate.id, seasonTemplate);
        }
        seasonTemplateMap = seasonTemplates;

        mapList = ConfigReader.read(ConfigFile.usual_QinPalace_rgCondition);
        Map<Integer, RgConditionTemplate> conditionTemplates = new HashMap<>();
        for (Map<String, String> map : mapList) {
            RgConditionTemplate seasonTemplate = new RgConditionTemplate(map);
            conditionTemplates.put(seasonTemplate.id, seasonTemplate);
        }
        conditionTemplateMap = conditionTemplates;
    }

    @Override
    public void clearConfigData() {
        talentTemplateMap.clear();
        rewardTemplateMap.clear();
        wayModelTemplateMap.clear();
        randomWayModelTemplateList.clear();
        storeyTemplateMap.clear();
        equipDropTemplateMap.clear();
        monsterGroupTemplateMap.clear();
        monsterGroupTemplateByTypeMap.clear();
        chatEventsTemplateMap.clear();
        chatEventsWeightMap.clear();
        specialEventsTemplateMap.clear();
        equipTemplateMap.clear();
        equipQualityInfoMap.clear();
        damnationTemplateMap.clear();
        damnationTemplateWeightMap.clear();
        infectTemplateMap.clear();
        infectTemplateWeightMap.clear();
        worldLevelAttTemplateTable.clear();
        seasonTemplateMap.clear();
        conditionTemplateMap.clear();
    }

    // 下面都是无脑get


    public static RgStoreyTemplate getRgStoreyTemplate(int levelId, int storeyId) {
        QinPalaceGlobalData globalData = GlobalDataManager.getData(GlobalDataType.QinPalace);
        Map<Integer, Map<Integer, RgStoreyTemplate>> integerMapMap = storeyTemplateMap.get(globalData.getSeasonTemplate().templateId);
        Map<Integer, RgStoreyTemplate> templateMap = integerMapMap.get(levelId);
        if (templateMap == null) {
            return null;
        }
        return templateMap.get(storeyId);
    }

    public static Map<Integer, RgStoreyTemplate> getStoreyTemplateMap(int levelId) {
        QinPalaceGlobalData globalData = GlobalDataManager.getData(GlobalDataType.QinPalace);
        return storeyTemplateMap.get(globalData.getSeasonTemplate().templateId).get(levelId);
    }

    public static Map<Integer, Map<Integer, RgStoreyTemplate>> getAllStoreyTemplateMap() {
        QinPalaceGlobalData globalData = GlobalDataManager.getData(GlobalDataType.QinPalace);
        return storeyTemplateMap.get(globalData.getSeasonTemplate().templateId);
    }

    public static Map<Integer, RgTalentTemplate> getTalentTemplateMap() {
        QinPalaceGlobalData globalData = GlobalDataManager.getData(GlobalDataType.QinPalace);
        return talentTemplateMap.get(globalData.getSeasonTemplate().templateId);
    }

    public static Map<Integer, RgEquipTemplate> getEquipTemplateMap() {
        return equipTemplateMap;
    }

    public static Map<Integer, RgDamnationTemplate> getDamnationTemplateMap() {
        return damnationTemplateMap;
    }

    public static Map<RgDamnationTemplate, Integer> getDamnationTemplateWeightMap() {
        return damnationTemplateWeightMap;
    }

    public static Map<Integer, RgRewardTemplate> getRewardTemplateMap() {
        Map<Integer, RgRewardTemplate> result = new HashMap<>();
        QinPalaceGlobalData globalData = GlobalDataManager.getData(GlobalDataType.QinPalace);
        if(rewardTemplateMap.containsKey(globalData.getSeasonTemplate().templateId)){
            result.putAll(rewardTemplateMap.get(globalData.getSeasonTemplate().templateId));
        }
        if(rewardTemplateMap.containsKey(-1)){
            result.putAll(rewardTemplateMap.get(-1));
        }
        return result;
    }

    public static Map<Integer, RgEquipDropTemplate> getEquipDropTemplateMap() {
        return equipDropTemplateMap;
    }

    public static Map<Integer, RgWayModelTemplate> getWayModelTemplateMap() {
        return wayModelTemplateMap;
    }

    public static List<RgWayModelTemplate> getRandomWayModelTemplateList() {
        return randomWayModelTemplateList;
    }

    public static Map<RgChatEventsTemplate, Integer> getChatEventsWeightMap() {
        return chatEventsWeightMap;
    }

    public static Map<Integer, RgChatEventsTemplate> getChatEventsTemplateMap() {
        return chatEventsTemplateMap;
    }

    public static Table<Integer, Integer, Map<Integer, RgMonsterGrowthCoeTemplate>> getWorldLevelAttTemplateTable() {
        return worldLevelAttTemplateTable;
    }

    public static Map<Integer, RgInfectTemplate> getInfectTemplateMap() {
        return infectTemplateMap;
    }

    public static Map<RgInfectTemplate, Integer> getInfectTemplateWeightMap() {
        return infectTemplateWeightMap;
    }

    public static RgConstant getRgConstant() {
        return rgConstant;
    }

    /**
     * 根据怪物类型获取怪物组
     */
    public static List<RgMonsterGroupTemplate> getRgMonsterGroupTemplateListByType(RgEventType eventType) {
        return monsterGroupTemplateByTypeMap.get(eventType);
    }

    /**
     * 获取怪物站位信息
     */
    public static RgMonsterGroupTemplate getRgMonsterGroupTemplate(int groupId) {
        return monsterGroupTemplateMap.get(groupId);
    }

    public static Map<Integer, RgSpecialEventsTemplate> getSpecialEventsTemplateMap() {
        return specialEventsTemplateMap;
    }

    public static int getNothingSpecialEventId() {
        return nothingSpecialEventId;
    }

    public static Map<Integer, RgSeasonTemplate> getSeasonTemplateMap() {
        return seasonTemplateMap;
    }

    public static Map<Integer, RgConditionTemplate> getConditionTemplateMap() {
        return conditionTemplateMap;
    }

    public static Map<Integer, List<Integer>> getEquipQualityInfoMap() {
        return equipQualityInfoMap;
    }


    @Override
    public boolean isPreSystemStartup() {
        return true;
    }
}
